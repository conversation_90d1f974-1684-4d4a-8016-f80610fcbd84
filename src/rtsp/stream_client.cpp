#include "../../include/stream_client.h"
#include "../../include/common.h"
#include <sstream>
#include <thread>
#include <chrono>

#ifdef USE_FFMPEG
#include <cstring>
#endif

StreamClient::StreamClient() 
    : status_(StreamClientStatus::DISCONNECTED)
    , initialized_(false)
    , shouldStop_(false) {
#ifdef USE_FFMPEG
    // 初始化FFmpeg网络组件
    static std::once_flag networkInitFlag;
    std::call_once(networkInitFlag, []() {
        avformat_network_init();
        Utils::logInfo("FFmpeg网络组件初始化完成");
    });
#endif
}

StreamClient::~StreamClient() {
    disconnect();
    cleanupFFmpeg();
}

bool StreamClient::initialize(const StreamClientConfig& config) {
    std::lock_guard<std::mutex> lock(connectionMutex_);
    
    if (initialized_) {
        logError("推流客户端已经初始化");
        return false;
    }
    
    config_ = config;
    
#ifdef USE_FFMPEG
    if (!initializeFFmpeg()) {
        logError("FFmpeg初始化失败");
        return false;
    }
    
    if (!configureOutput()) {
        logError("输出配置失败");
        cleanupFFmpeg();
        return false;
    }
#else
    logError("FFmpeg支持未启用，无法初始化推流客户端");
    return false;
#endif
    
    initialized_ = true;
    stats_.reset();
    
    logInfo("推流客户端初始化成功: " + config_.pushUrl);
    return true;
}

bool StreamClient::connect() {
    std::lock_guard<std::mutex> lock(connectionMutex_);
    
    if (!initialized_) {
        logError("推流客户端未初始化");
        return false;
    }
    
    if (isConnected()) {
        logInfo("推流客户端已连接");
        return true;
    }
    
    status_ = StreamClientStatus::CONNECTING;
    
#ifdef USE_FFMPEG
    // 打开输出流
    int ret = avformat_write_header(formatContext_, nullptr);
    if (ret < 0) {
        logError("无法写入流头部", ret);
        status_ = StreamClientStatus::ERROR;
        return false;
    }
    
    status_ = StreamClientStatus::CONNECTED;
    stats_.reset();
    
    // 启动重连线程
    shouldStop_ = false;
    reconnectThread_ = std::make_unique<std::thread>(&StreamClient::reconnectWorker, this);
    
    logInfo("推流客户端连接成功");
    return true;
#else
    status_ = StreamClientStatus::ERROR;
    return false;
#endif
}

void StreamClient::disconnect() {
    std::lock_guard<std::mutex> lock(connectionMutex_);
    
    shouldStop_ = true;
    
    if (reconnectThread_ && reconnectThread_->joinable()) {
        reconnectCondition_.notify_all();
        reconnectThread_->join();
        reconnectThread_.reset();
    }
    
#ifdef USE_FFMPEG
    if (formatContext_) {
        if (status_ == StreamClientStatus::STREAMING || status_ == StreamClientStatus::CONNECTED) {
            av_write_trailer(formatContext_);
        }
    }
#endif
    
    status_ = StreamClientStatus::DISCONNECTED;
    logInfo("推流客户端已断开连接");
}

bool StreamClient::sendPacket(const EncodedPacket& packet) {
    if (!isConnected() || packet.size == 0 || !packet.data) {
        return false;
    }
    
#ifdef USE_FFMPEG
    // 创建AVPacket
    AVPacket* avPacket = av_packet_alloc();
    if (!avPacket) {
        logError("无法分配AVPacket");
        return false;
    }
    
    // 复制数据
    av_packet_from_data(avPacket, packet.data.get(), packet.size);
    avPacket->stream_index = videoStream_->index;
    avPacket->pts = packet.pts;
    avPacket->dts = packet.dts;
    
    if (packet.isKeyFrame) {
        avPacket->flags |= AV_PKT_FLAG_KEY;
    }
    
    // 发送数据包
    int ret = av_interleaved_write_frame(formatContext_, avPacket);
    av_packet_free(&avPacket);
    
    if (ret < 0) {
        logError("发送数据包失败", ret);
        
        // 如果是网络错误，尝试重连
        if (ret == AVERROR(EPIPE) || ret == AVERROR(ECONNRESET)) {
            status_ = StreamClientStatus::RECONNECTING;
            reconnectCondition_.notify_one();
        }
        
        stats_.droppedFrames++;
        return false;
    }
    
    // 更新统计信息
    stats_.sentFrames++;
    stats_.totalBytes += packet.size;
    
    if (status_ == StreamClientStatus::CONNECTED) {
        status_ = StreamClientStatus::STREAMING;
    }
    
    updateStats();
    return true;
#else
    return false;
#endif
}

bool StreamClient::updateConfig(const StreamClientConfig& config) {
    if (isConnected()) {
        logError("无法在连接状态下更新配置");
        return false;
    }
    
    config_ = config;
    
    // 重新初始化
    cleanupFFmpeg();
    initialized_ = false;
    
    return initialize(config);
}

#ifdef USE_FFMPEG
bool StreamClient::initializeFFmpeg() {
    // 分配输出格式上下文
    // 对于RTMP推流，明确指定FLV格式
    const char* format_name = nullptr;
    if (config_.pushUrl.find("rtmp://") == 0) {
        format_name = "flv";  // RTMP使用FLV格式
    } else if (config_.pushUrl.find("rtsp://") == 0) {
        format_name = "rtsp"; // RTSP格式
    }

    int ret = avformat_alloc_output_context2(&formatContext_, nullptr, format_name, config_.pushUrl.c_str());
    if (ret < 0) {
        logError("无法分配输出格式上下文", ret);
        return false;
    }
    
    // 分配数据包
    packet_ = av_packet_alloc();
    if (!packet_) {
        logError("无法分配AVPacket");
        return false;
    }
    
    return true;
}

bool StreamClient::configureOutput() {
    // 查找编码器
    const AVCodec* codec = avcodec_find_encoder(AV_CODEC_ID_H264);
    if (!codec) {
        logError("找不到H.264编码器");
        return false;
    }
    
    // 创建视频流
    videoStream_ = avformat_new_stream(formatContext_, codec);
    if (!videoStream_) {
        logError("无法创建视频流");
        return false;
    }
    
    // 创建编码器上下文
    codecContext_ = avcodec_alloc_context3(codec);
    if (!codecContext_) {
        logError("无法分配编码器上下文");
        return false;
    }
    
    // 配置编码器参数
    codecContext_->width = config_.width;
    codecContext_->height = config_.height;
    codecContext_->time_base = {1, config_.fps};
    codecContext_->framerate = {config_.fps, 1};
    codecContext_->bit_rate = config_.bitrate;
    codecContext_->pix_fmt = AV_PIX_FMT_YUV420P;
    codecContext_->gop_size = 30;
    codecContext_->max_b_frames = 0;
    
    // 设置编码选项
    av_opt_set(codecContext_->priv_data, "preset", config_.preset.c_str(), 0);
    av_opt_set(codecContext_->priv_data, "profile", config_.profile.c_str(), 0);
    
    // 复制参数到流
    avcodec_parameters_from_context(videoStream_->codecpar, codecContext_);
    videoStream_->time_base = codecContext_->time_base;
    
    // 打开输出URL
    if (!(formatContext_->oformat->flags & AVFMT_NOFILE)) {
        int ret = avio_open(&formatContext_->pb, config_.pushUrl.c_str(), AVIO_FLAG_WRITE);
        if (ret < 0) {
            logError("无法打开输出URL: " + config_.pushUrl, ret);
            return false;
        }
    }
    
    return true;
}
#endif

void StreamClient::reconnectWorker() {
    while (!shouldStop_) {
        std::unique_lock<std::mutex> lock(connectionMutex_);
        
        // 等待重连信号或超时
        reconnectCondition_.wait_for(lock, std::chrono::milliseconds(config_.reconnectIntervalMs),
                                   [this] { return shouldStop_ || status_ == StreamClientStatus::RECONNECTING; });
        
        if (shouldStop_) {
            break;
        }
        
        if (status_ == StreamClientStatus::RECONNECTING) {
            logInfo("尝试重新连接...");
            
            if (attemptReconnect()) {
                logInfo("重连成功");
                status_ = StreamClientStatus::CONNECTED;
                stats_.retryCount = 0;
            } else {
                stats_.retryCount++;
                if (stats_.retryCount >= config_.maxRetries) {
                    logError("达到最大重试次数，停止重连");
                    status_ = StreamClientStatus::ERROR;
                } else {
                    logInfo("重连失败，将在 " + std::to_string(config_.reconnectIntervalMs) + "ms 后重试");
                }
            }
        }
    }
}

bool StreamClient::attemptReconnect() {
#ifdef USE_FFMPEG
    // 关闭当前连接
    if (formatContext_ && formatContext_->pb) {
        avio_closep(&formatContext_->pb);
    }
    
    // 重新打开输出URL
    if (!(formatContext_->oformat->flags & AVFMT_NOFILE)) {
        int ret = avio_open(&formatContext_->pb, config_.pushUrl.c_str(), AVIO_FLAG_WRITE);
        if (ret < 0) {
            logError("重连时无法打开输出URL", ret);
            return false;
        }
    }
    
    // 重新写入头部
    int ret = avformat_write_header(formatContext_, nullptr);
    if (ret < 0) {
        logError("重连时无法写入流头部", ret);
        return false;
    }
    
    return true;
#else
    return false;
#endif
}

void StreamClient::updateStats() {
    auto now = std::chrono::steady_clock::now();
    auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(now - stats_.lastFrameTime).count();
    
    if (timeDiff > 0) {
        stats_.currentFPS = 1000.0 / timeDiff;
    }
    
    stats_.lastFrameTime = now;
    
    // 计算平均码率
    auto totalTime = std::chrono::duration_cast<std::chrono::seconds>(now - stats_.startTime).count();
    if (totalTime > 0) {
        stats_.avgBitrate = (stats_.totalBytes * 8.0) / totalTime;
    }
}

void StreamClient::cleanupFFmpeg() {
#ifdef USE_FFMPEG
    if (packet_) {
        av_packet_free(&packet_);
    }
    
    if (codecContext_) {
        avcodec_free_context(&codecContext_);
    }
    
    if (formatContext_) {
        if (formatContext_->pb) {
            avio_closep(&formatContext_->pb);
        }
        avformat_free_context(formatContext_);
        formatContext_ = nullptr;
    }
    
    videoStream_ = nullptr;
#endif
}

void StreamClient::logError(const std::string& message, int errorCode) const {
    std::string fullMessage = "StreamClient: " + message;
    
#ifdef USE_FFMPEG
    if (errorCode != 0) {
        fullMessage += " (" + getFFmpegError(errorCode) + ")";
    }
#endif
    
    Utils::logError(fullMessage);
}

void StreamClient::logInfo(const std::string& message) const {
    Utils::logInfo("StreamClient: " + message);
}

#ifdef USE_FFMPEG
std::string StreamClient::getFFmpegError(int errorCode) const {
    char errorBuffer[AV_ERROR_MAX_STRING_SIZE];
    av_strerror(errorCode, errorBuffer, AV_ERROR_MAX_STRING_SIZE);
    return std::string(errorBuffer);
}
#else
std::string StreamClient::getFFmpegError(int errorCode) const {
    return "FFmpeg error code: " + std::to_string(errorCode);
}
#endif
